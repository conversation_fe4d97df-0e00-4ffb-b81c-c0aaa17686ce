import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Button, Dialog, Text, Portal, useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import AccountBenefitsList from './AccountBenefitsList';
import { MaterialCommunityIcons } from '@expo/vector-icons';

type SignUpPromptDialogProps = {
    visible: boolean;
    onDismiss: () => void;
};

const SignUpPromptDialog: React.FC<SignUpPromptDialogProps> = ({ visible, onDismiss }) => {
    const theme = useTheme();
    const navigation = useNavigation<NativeStackNavigationProp<any>>();

    const handleSignUp = () => {
        onDismiss();
        navigation.navigate("AuthSignUpForm");
    };

    const handleGuestContinue = () => {
        onDismiss();
    };

    return (
        <Portal>
            <Dialog visible={visible} onDismiss={onDismiss} style={styles.dialog}>
                <Dialog.Content style={styles.content}>
                    <View style={styles.iconContainer}>
                        <MaterialCommunityIcons
                            name="account-plus"
                            size={80}
                            color={theme.colors.primary}
                        />
                    </View>
                    <Text variant="headlineSmall" style={styles.title}>
                        Créer un compte gratuit
                    </Text>
                    <Text variant="bodyLarge" style={styles.description}>
                        Accédez à toutes les fonctionnalités de PharmaChainage
                    </Text>

                    <View style={styles.benefitsContainer}>
                        <AccountBenefitsList
                            variant="signup"
                            titleStyle={styles.benefitText}
                            itemStyle={styles.benefitItem}
                        />
                    </View>

                </Dialog.Content>
                <Dialog.Actions style={styles.actions}>
                    <Button
                        mode="text"
                        onPress={handleGuestContinue}
                        style={styles.guestButton}
                    >
                        Mode invité
                    </Button>
                    <Button
                        mode="contained"
                        onPress={handleSignUp}
                        style={styles.createAccountButton}
                    >
                        Créer un compte
                    </Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
    dialog: {
        borderRadius: 16,
    },
    content: {
        alignItems: 'center',
        paddingVertical: 24,
        paddingHorizontal: 16,
    },
    iconContainer: {
        marginBottom: 16,
    },
    title: {
        textAlign: 'center',
        fontWeight: 'bold',
        marginBottom: 8,
    },
    description: {
        textAlign: 'center',
        marginBottom: 24,
        paddingHorizontal: 8,
    },
    benefitsContainer: {
        width: '100%',
        marginBottom: 32,
    },
    benefitItem: {
        paddingVertical: 2,
    },
    benefitText: {
        fontSize: 16,
    },
    actions: {
        paddingHorizontal: 16,
        paddingBottom: 16,
        gap: 12,
    },
    createAccountButton: {
        flex: 1,
        paddingVertical: 4,
    },
    guestButton: {
        flex: 1,
        paddingVertical: 4,
    },
});

export default SignUpPromptDialog;
